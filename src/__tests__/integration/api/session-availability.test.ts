/**
 * Session Availability API 路由測試
 * 測試 /api/session-availability 端點的完整功能
 */

import { NextRequest } from 'next/server';
import { GET } from '@/app/api/session-availability/route';

// Mock dependencies
jest.mock('@/lib/google-sheets', () => ({
  getSheetData: jest.fn(),
}));

// Mock console methods to reduce test noise
const originalConsoleLog = console.log;
const originalConsoleError = console.error;

beforeAll(() => {
  console.log = jest.fn();
  console.error = jest.fn();
});

afterAll(() => {
  console.log = originalConsoleLog;
  console.error = originalConsoleError;
});

describe('/api/session-availability', () => {
  beforeEach(() => {
    jest.clearAllMocks();

    // Setup mock return values
    const { getSheetData } = require('@/lib/google-sheets');
    getSheetData.mockResolvedValue(mockRegistrationData);
  });

  const mockRegistrationData = [
    // Header row - 需要完整的 29 個欄位（A-AC），AC 欄位在索引 28
    ['A', 'B場次時間', 'C', 'D參加方式', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z', 'AA', 'AB', 'AC報名狀態'],
    // Data rows - 確保 AC 欄位（索引 28）有正確的報名狀態
    ['張三', '台北 07/20（日）13:20', '', '個人報名', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '1'], // 已確認
    ['李四', '台北 07/20（日）13:20', '', '個人報名', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '1'], // 已確認
    ['王五', '台北 07/20（日）13:20', '', '雙人團報', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '2'], // 保留中（雙人）
    ['趙六', '台北 07/20（日）15:20', '', '個人報名', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '1'], // 已確認
    ['錢七', '台中 07/18（五）19:20', '', '個人報名', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '3'], // 已取消
    ['孫八', '台中 07/18（五）19:20', '', '個人報名', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '1'], // 已確認
  ];

  describe('成功案例', () => {
    test('應該正確計算場次名額', async () => {
      const { getSheetData } = require('@/lib/google-sheets');
      getSheetData.mockResolvedValue(mockRegistrationData);

      const request = new NextRequest('http://localhost:3000/api/session-availability');
      const response = await GET();
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
      expect(data.data).toBeInstanceOf(Array);
      expect(data.cached).toBe(false);

      // 檢查特定場次的計算結果
      const taipei1320 = data.data.find((session: any) => 
        session.sessionTime === '台北 07/20（日）13:20'
      );
      expect(taipei1320).toBeDefined();
      expect(taipei1320.maxCapacity).toBe(8);
      expect(taipei1320.registeredCount).toBe(4); // 張三(1) + 李四(1) + 王五(2) = 3個人，但王五是雙人所以算2個名額
      expect(taipei1320.availableSpots).toBe(4); // 8 - 4 = 4
      expect(taipei1320.isAvailable).toBe(true);
    });

    test('應該正確處理快取機制', async () => {
      const { getSheetData } = require('@/lib/google-sheets');
      getSheetData.mockResolvedValue(mockRegistrationData);

      // 第一次請求
      const request1 = new NextRequest('http://localhost:3000/api/session-availability');
      const response1 = await GET();
      const data1 = await response1.json();

      expect(data1.cached).toBe(false);
      expect(getSheetData).toHaveBeenCalledTimes(1);

      // 第二次請求（應該使用快取）
      const request2 = new NextRequest('http://localhost:3000/api/session-availability');
      const response2 = await GET();
      const data2 = await response2.json();

      expect(data2.cached).toBe(true);
      expect(data2.cacheAge).toBeGreaterThanOrEqual(0);
      expect(getSheetData).toHaveBeenCalledTimes(1); // 沒有再次調用
    });

    test('應該正確處理空資料', async () => {
      const { getSheetData } = require('@/lib/google-sheets');
      getSheetData.mockResolvedValue([]);

      const request = new NextRequest('http://localhost:3000/api/session-availability');
      const response = await GET();
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
      expect(data.data).toBeInstanceOf(Array);

      // 所有場次都應該顯示滿額
      data.data.forEach((session: any) => {
        expect(session.registeredCount).toBe(0);
        expect(session.availableSpots).toBe(session.maxCapacity);
        expect(session.isAvailable).toBe(true);
      });
    });

    test('應該正確計算雙人報名的名額', async () => {
      const pairRegistrationData = [
        ['A', 'B場次時間', 'C', 'D參加方式', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z', 'AA', 'AB', 'AC報名狀態'],
        ['張三', '台北 07/20（日）13:20', '', '雙人團報', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '1'], // 雙人報名，佔2個名額
        ['李四', '台北 07/20（日）13:20', '', '個人報名', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '1'], // 個人報名，佔1個名額
      ];

      const { getSheetData } = require('@/lib/google-sheets');
      getSheetData.mockResolvedValue(pairRegistrationData);

      const request = new NextRequest('http://localhost:3000/api/session-availability');
      const response = await GET();
      const data = await response.json();

      const taipei1320 = data.data.find((session: any) => 
        session.sessionTime === '台北 07/20（日）13:20'
      );
      
      expect(taipei1320.registeredCount).toBe(3); // 2 (雙人) + 1 (個人) = 3
      expect(taipei1320.availableSpots).toBe(5); // 8 - 3 = 5
    });

    test('應該正確處理取消狀態的報名', async () => {
      const cancelledRegistrationData = [
        ['A', 'B場次時間', 'C', 'D參加方式', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z', 'AA', 'AB', 'AC報名狀態'],
        ['張三', '台北 07/20（日）13:20', '', '個人報名', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '1'], // 已確認
        ['李四', '台北 07/20（日）13:20', '', '個人報名', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '3'], // 已取消，不計入名額
        ['王五', '台北 07/20（日）13:20', '', '個人報名', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '2'], // 保留中
      ];

      const { getSheetData } = require('@/lib/google-sheets');
      getSheetData.mockResolvedValue(cancelledRegistrationData);

      const request = new NextRequest('http://localhost:3000/api/session-availability');
      const response = await GET();
      const data = await response.json();

      const taipei1320 = data.data.find((session: any) => 
        session.sessionTime === '台北 07/20（日）13:20'
      );
      
      expect(taipei1320.registeredCount).toBe(2); // 只計算狀態1和2，不計算狀態3
      expect(taipei1320.availableSpots).toBe(6); // 8 - 2 = 6
    });
  });

  describe('可用性顯示邏輯', () => {
    test('應該在剩餘名額少於3時顯示可用性', async () => {
      const nearFullData = [
        ['姓名', '場次時間', '參加方式', '...', '報名狀態'],
        ['張三', '台北 07/20（日）13:20', 'individual', '...', '1'],
        ['李四', '台北 07/20（日）13:20', 'individual', '...', '1'],
        ['王五', '台北 07/20（日）13:20', 'individual', '...', '1'],
        ['趙六', '台北 07/20（日）13:20', 'individual', '...', '1'],
        ['錢七', '台北 07/20（日）13:20', 'individual', '...', '1'],
        ['孫八', '台北 07/20（日）13:20', 'individual', '...', '1'], // 6個人已報名
      ];

      const { getSheetData } = require('@/lib/google-sheets');
      getSheetData.mockResolvedValue(nearFullData);

      const request = new NextRequest('http://localhost:3000/api/session-availability');
      const response = await GET();
      const data = await response.json();

      const taipei1320 = data.data.find((session: any) => 
        session.sessionTime === '台北 07/20（日）13:20'
      );
      
      expect(taipei1320.availableSpots).toBe(2); // 8 - 6 = 2
      expect(taipei1320.showAvailability).toBe(true); // 剩餘2個名額，少於3
      expect(taipei1320.isAvailable).toBe(true);
    });

    test('應該在名額充足時不顯示可用性', async () => {
      const sufficientData = [
        ['姓名', '場次時間', '參加方式', '...', '報名狀態'],
        ['張三', '台北 07/20（日）13:20', 'individual', '...', '1'],
        ['李四', '台北 07/20（日）13:20', 'individual', '...', '1'], // 只有2個人報名
      ];

      const { getSheetData } = require('@/lib/google-sheets');
      getSheetData.mockResolvedValue(sufficientData);

      const request = new NextRequest('http://localhost:3000/api/session-availability');
      const response = await GET();
      const data = await response.json();

      const taipei1320 = data.data.find((session: any) => 
        session.sessionTime === '台北 07/20（日）13:20'
      );
      
      expect(taipei1320.availableSpots).toBe(6); // 8 - 2 = 6
      expect(taipei1320.showAvailability).toBe(false); // 剩餘6個名額，大於等於3
      expect(taipei1320.isAvailable).toBe(true);
    });

    test('應該正確處理額滿場次', async () => {
      const fullData = [
        ['姓名', '場次時間', '參加方式', '...', '報名狀態'],
        ['張三', '台北 07/20（日）13:20', 'individual', '...', '1'],
        ['李四', '台北 07/20（日）13:20', 'individual', '...', '1'],
        ['王五', '台北 07/20（日）13:20', 'individual', '...', '1'],
        ['趙六', '台北 07/20（日）13:20', 'individual', '...', '1'],
        ['錢七', '台北 07/20（日）13:20', 'individual', '...', '1'],
        ['孫八', '台北 07/20（日）13:20', 'individual', '...', '1'],
        ['周九', '台北 07/20（日）13:20', 'individual', '...', '1'],
        ['吳十', '台北 07/20（日）13:20', 'individual', '...', '1'], // 8個人，額滿
      ];

      const { getSheetData } = require('@/lib/google-sheets');
      getSheetData.mockResolvedValue(fullData);

      const request = new NextRequest('http://localhost:3000/api/session-availability');
      const response = await GET();
      const data = await response.json();

      const taipei1320 = data.data.find((session: any) => 
        session.sessionTime === '台北 07/20（日）13:20'
      );
      
      expect(taipei1320.availableSpots).toBe(0);
      expect(taipei1320.showAvailability).toBe(false); // 額滿時不顯示
      expect(taipei1320.isAvailable).toBe(false);
    });
  });

  describe('錯誤處理', () => {
    test('應該處理 Google Sheets 查詢失敗', async () => {
      const { getSheetData } = require('@/lib/google-sheets');
      getSheetData.mockRejectedValue(new Error('Google Sheets API 錯誤'));

      const request = new NextRequest('http://localhost:3000/api/session-availability');
      const response = await GET();
      const data = await response.json();

      expect(response.status).toBe(500);
      expect(data.success).toBe(false);
      expect(data.error).toContain('查詢場次名額失敗');
    });

    test('應該處理資料格式錯誤', async () => {
      const { getSheetData } = require('@/lib/google-sheets');
      getSheetData.mockResolvedValue(null); // 返回 null

      const request = new NextRequest('http://localhost:3000/api/session-availability');
      const response = await GET();
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
      // 應該返回預設的空名額資料
      expect(data.data).toBeInstanceOf(Array);
    });
  });

  describe('特殊場次處理', () => {
    test('應該正確處理"有意願但無合適時間地點"場次', async () => {
      const customTimeData = [
        ['姓名', '場次時間', '參加方式', '...', '報名狀態'],
        ['張三', '有意願但無合適時間地點', 'individual', '...', '1'],
        ['李四', '有意願但無合適時間地點', 'individual', '...', '1'],
      ];

      const { getSheetData } = require('@/lib/google-sheets');
      getSheetData.mockResolvedValue(customTimeData);

      const request = new NextRequest('http://localhost:3000/api/session-availability');
      const response = await GET();
      const data = await response.json();

      const customSession = data.data.find((session: any) => 
        session.sessionTime === '有意願但無合適時間地點'
      );
      
      expect(customSession.maxCapacity).toBe(999); // 無限制
      expect(customSession.registeredCount).toBe(2);
      expect(customSession.availableSpots).toBe(997); // 999 - 2
      expect(customSession.isAvailable).toBe(true);
      expect(customSession.showAvailability).toBe(false); // 永遠不顯示可用性
    });
  });
});
