import { NextResponse } from 'next/server';
import { getSheetData, getSheetsClient } from '@/lib/google-sheets';
import { createPaymentRequest, calculateATMExpireDate } from '@/lib/payuni';
import { PAYUNI_CONFIG } from '@/config/environment-config';

export async function POST(request: Request) {
  try {
    const body = await request.json();
    const { orderNo } = body;

    console.log(`🔄 收到重新付款請求，訂單號碼: ${orderNo}`);

    if (!orderNo) {
      console.log('❌ 缺少訂單號碼');
      return NextResponse.json(
        { error: '缺少訂單號碼' },
        { status: 400 }
      );
    }

    // 查詢訂單資料
    const sheetName = '工作表1';
    console.log(`📊 開始從 Google Sheets 查詢訂單: ${orderNo}`);

    try {
      const sheetData = await getSheetData(`${sheetName}!A:AD`);

      if (!sheetData || sheetData.length === 0) {
        return NextResponse.json(
          { error: '找不到訂單資料' },
          { status: 404 }
        );
      }

      // 找到對應的訂單 (訂單號碼在 V 欄，索引 21)
      const orderRow = sheetData.find(row => row[21] === orderNo);

      if (!orderRow) {
        return NextResponse.json(
          { error: '找不到指定的訂單' },
          { status: 404 }
        );
      }

      // 檢查付款狀態 (W欄位，索引22)
      const paymentStatus = orderRow[22] || ''; // W: 付款狀態
      console.log(`🔍 檢查訂單 ${orderNo} 付款狀態: "${paymentStatus}"`);

      if (paymentStatus === '已完成' || paymentStatus === '已付款') {
        console.log(`❌ 訂單 ${orderNo} 已完成付款，無法重新付款`);
        return NextResponse.json(
          { error: '此訂單已完成付款' },
          { status: 400 }
        );
      }

      // 允許的重新付款狀態：待付款、付款失敗、付款取消、訂單待確認、重新付款中
      const allowedStatuses = ['待付款', '付款失敗', '付款取消', '訂單待確認', '重新付款中', ''];
      if (!allowedStatuses.includes(paymentStatus)) {
        console.log(`❌ 訂單 ${orderNo} 狀態 "${paymentStatus}" 不允許重新付款`);
        return NextResponse.json(
          { error: `訂單狀態 "${paymentStatus}" 不允許重新付款` },
          { status: 400 }
        );
      }

      console.log(`✅ 訂單 ${orderNo} 狀態 "${paymentStatus}" 允許重新付款`);

      // 解析訂單資料 (新欄位配置: V為訂單號碼, W為付款狀態, X為應付金額)
      const orderData = {
        orderNo: orderRow[21] || '',                    // V: 訂單號碼 (索引21)
        name: orderRow[4] || '',                        // E: 姓名
        email: orderRow[5] || '',                       // F: Email
        eventPrice: parseInt(orderRow[23]) || 0,        // X: 應付金額 (索引23)
        eventName: '錶匠體驗',                          // 活動名稱
      };

      // 生成新的訂單號碼，避免 PayUni UPP01007 錯誤（已存在相同商店訂單編號）
      // 直接生成新的訂單號碼，而不是添加後綴，避免長度限制問題
      const newOrderNo = `pangea_retry_${Date.now()}`;
      console.log(`🔄 重新付款生成新訂單號碼: ${orderData.orderNo} → ${newOrderNo}`);

      // 在 Google Sheets 中記錄重新付款狀態，但保留原訂單號碼
      // 只有當 PayUni 真正接收到訂單後才更新主訂單號碼
      await updateRetryPaymentStatus(orderData.orderNo, newOrderNo);

      // 計算 ATM 轉帳動態到期日期（基於台灣時間 14:00 判斷）
      const expireDate = calculateATMExpireDate();

      // 建立 PayUni 付款請求
      const tradeData = {
        MerID: PAYUNI_CONFIG.getMerchantId(),
        MerTradeNo: newOrderNo, // 使用新的訂單編號
        TradeAmt: orderData.eventPrice, // 訂單金額
        Timestamp: Math.floor(Date.now() / 1000), // 時間戳記
        ProdDesc: orderData.eventName, // 商品說明
        UsrMail: orderData.email, // 消費者信箱
        Lang: 'zh-tw', // 語系
        NotifyURL: process.env.PAYUNI_NOTIFY_URL || '', // 背景通知網址
        ReturnURL: `${process.env.NEXT_PUBLIC_BASE_URL}/api/payment/callback`, // 前景通知網址
        BackURL: `${process.env.NEXT_PUBLIC_BASE_URL}/api/payment/callback`, // 返回商店按鈕網址
        // 付款方式設定：只開啟信用卡一次付清 & ATM 轉帳
        PaymentType: 'credit,atm', // credit=信用卡, atm=ATM轉帳
        CreditType: '1', // 1=一次付清
        ExpireDate: expireDate, // ATM 轉帳有效期限：當日+2天
      };

      // 產生加密後的付款參數
      const paymentRequest = createPaymentRequest(tradeData);

      console.log(`✅ 重新付款請求準備完成，原訂單: ${orderData.orderNo}，新訂單: ${newOrderNo}`);

      // 返回付款資料，讓前端處理跳轉
      return NextResponse.json({
        success: true,
        message: `重新付款請求已建立，新訂單號碼: ${newOrderNo}`,
        newOrderNo: newOrderNo,
        MerID: paymentRequest.MerID,
        EncryptInfo: paymentRequest.EncryptInfo,
        HashInfo: paymentRequest.HashInfo,
        Version: '1.0',
        paymentUrl: PAYUNI_CONFIG.getApiUrl(),
        paymentData: {
          action: PAYUNI_CONFIG.getApiUrl(),
          MerID: paymentRequest.MerID,
          EncryptInfo: paymentRequest.EncryptInfo,
          HashInfo: paymentRequest.HashInfo,
          Version: '1.0'
        }
      });

    } catch (sheetError) {
      console.error('Google Sheets 查詢失敗:', sheetError);
      return NextResponse.json(
        { error: '查詢訂單資料失敗' },
        { status: 500 }
      );
    }

  } catch (error) {
    console.error('重新付款處理失敗:', error);
    return NextResponse.json(
      { error: '處理失敗，請稍後再試' },
      { status: 500 }
    );
  }
}

/**
 * 更新重新付款狀態，記錄新訂單號碼但保留原訂單號碼
 * @param originalOrderNo - 原訂單號碼
 * @param newOrderNo - 新訂單號碼
 */
async function updateRetryPaymentStatus(originalOrderNo: string, newOrderNo: string) {
  const sheetName = '工作表1';
  console.log(`📝 記錄重新付款狀態: ${originalOrderNo} → ${newOrderNo}`);

  // 1. 讀取所有訂單資料
  const sheetData = await getSheetData(`${sheetName}!A:AD`);

  if (!sheetData || sheetData.length === 0) {
    throw new Error('找不到訂單資料');
  }

  // 2. 找到對應的訂單行 (訂單號碼在 V 欄，索引 21)
  let targetRowIndex = -1;
  for (let i = 0; i < sheetData.length; i++) {
    if (sheetData[i][21] === originalOrderNo) { // V 欄位 (索引 21)
      targetRowIndex = i;
      break;
    }
  }

  if (targetRowIndex === -1) {
    throw new Error(`找不到訂單: ${originalOrderNo}`);
  }

  // 3. 計算實際的行號 (Google Sheets 從 1 開始計算)
  const actualRowNumber = targetRowIndex + 1;
  console.log(`📍 找到訂單 ${originalOrderNo} 在第 ${actualRowNumber} 行`);

  // 4. 更新 Google Sheets
  const sheets = getSheetsClient();

  // 更新付款狀態為「重新付款中」(W 欄位)
  await sheets.spreadsheets.values.update({
    spreadsheetId: process.env.GOOGLE_SHEET_ID,
    range: `${sheetName}!W${actualRowNumber}`,
    valueInputOption: 'USER_ENTERED',
    requestBody: {
      values: [['重新付款中']],
    },
  });

  // 在 AB 欄位記錄新的訂單號碼（臨時存儲）
  await sheets.spreadsheets.values.update({
    spreadsheetId: process.env.GOOGLE_SHEET_ID,
    range: `${sheetName}!AB${actualRowNumber}`,
    valueInputOption: 'USER_ENTERED',
    requestBody: {
      values: [[newOrderNo]],
    },
  });

  console.log(`✅ 成功記錄重新付款狀態: ${originalOrderNo}，新訂單號碼: ${newOrderNo}`);
}
