import React from 'react';

interface OrderData {
  orderNo: string;
  name: string;
  email: string;
  eventName: string;
  eventPrice: number;
  sessionTimes: string[];
  participationType: string;
  paymentStatus: string;
  paymentMethod: string;
  paymentCompletedAt: string;
  submittedAt: string;
  tradeStatus?: string;
  gateway?: string;
  dataSource?: string;
  payuniTradeNo?: string;
  payuniPayNo?: string;
  payuniRawData?: Record<string, unknown>;
}

interface OrderStatusResultProps {
  orderData: OrderData;
}

// 解析 PayUni API 回應中的 Result[0][...] 欄位
function parsePayUniResult(data: Record<string, unknown>) {
  const result: Record<string, unknown> = {};
  
  // 提取所有 Result[0][...] 欄位
  Object.keys(data).forEach(key => {
    const match = key.match(/^Result\[0\]\[(.+)\]$/);
    if (match) {
      result[match[1]] = data[key];
    }
  });
  
  return result;
}

// 格式化顯示值
function formatValue(key: string, value: unknown): string {
  if (value === null || value === undefined || value === '') {
    return '無資料';
  }

  switch (key) {
    case 'TradeAmt':
      return `NT$ ${parseInt(String(value || '0')).toLocaleString()}`;

    case 'TradeStatus':
      switch (value) {
        case '0': return '取號成功';
        case '1': return '已付款';
        case '2': return '付款失敗';
        case '3': return '付款取消';
        case '4': return '交易逾期';
        case '8': return '訂單待確認';
        case '9': return '待付款';
        default: return '未知狀態';
      }

    case 'PaymentType':
      switch (value) {
        case '1': return '信用卡';
        case '2': return 'ATM 轉帳';
        case '3': return '超商代碼';
        case '4': return '超商條碼';
        default: return `付款方式 ${value}`;
      }

    default:
      return String(value);
  }
}

export default function OrderStatusResult({ orderData }: OrderStatusResultProps) {
  const getStatusColor = (status: string) => {
    switch (status) {
      case '已完成':
      case '已付款':
        return 'text-green-600 bg-green-100';
      case '已退款':
        return 'text-purple-600 bg-purple-100';
      case '退款處理中':
        return 'text-orange-600 bg-orange-100';
      case '待付款':
        return 'text-yellow-600 bg-yellow-100';
      case '已取消':
      case '付款失敗':
        return 'text-red-600 bg-red-100';
      default:
        return 'text-gray-600 bg-gray-100';
    }
  };

  // 如果是 PayUni API 資料，解析原始資料來顯示重要資訊
  const payuniResult = orderData.payuniRawData ? parsePayUniResult(orderData.payuniRawData) : null;

  return (
    <div className="bg-white rounded-lg shadow-md p-6">
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-xl font-semibold text-gray-900">訂單詳情</h2>
        <span className={`px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(orderData.paymentStatus)}`}>
          {orderData.paymentStatus}
        </span>
      </div>

      {/* 退款資訊區塊 - 移到最前面 */}
      {(payuniResult && (payuniResult.RefundStatus === '2' || payuniResult.RefundStatus === '1' || payuniResult.RefundStatus === '8')) ? (
        <div className="bg-purple-50 border border-purple-200 rounded-lg p-4 mb-6">
          <h3 className="font-medium text-purple-900 mb-3 flex items-center">
            💰 退款資訊
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
            <div>
              <span className="text-purple-600 font-medium">退款狀態:</span>
              <p className="font-medium text-purple-900">
                {payuniResult.RefundStatus === '2' ? '已退款' :
                 payuniResult.RefundStatus === '1' ? '退款申請中' :
                 payuniResult.RefundStatus === '8' ? '退款處理中' : '未知狀態'}
              </p>
            </div>

            {(payuniResult.RefundAmt && String(payuniResult.RefundAmt) !== '0') ? (
              <div>
                <span className="text-purple-600 font-medium">退款金額:</span>
                <p className="font-medium text-purple-900">
                  NT$ {parseInt(String(payuniResult.RefundAmt || '0')).toLocaleString()}
                </p>
              </div>
            ) : null}

            {(payuniResult.RefundDay && String(payuniResult.RefundDay)) ? (
              <div>
                <span className="text-purple-600 font-medium">退款時間:</span>
                <p className="font-medium text-purple-900">{String(payuniResult.RefundDay)}</p>
              </div>
            ) : null}

            {(payuniResult.RemainAmt && String(payuniResult.RemainAmt) !== '0') ? (
              <div>
                <span className="text-purple-600 font-medium">剩餘金額:</span>
                <p className="font-medium text-purple-900">
                  NT$ {parseInt(String(payuniResult.RemainAmt || '0')).toLocaleString()}
                </p>
              </div>
            ) : null}
          </div>
        </div>
      ) : null}

      {/* 訂單資訊區塊 */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
        <h3 className="font-medium text-blue-900 mb-3 flex items-center">
          📋 訂單資訊
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
          <div>
            <span className="text-blue-600 font-medium">商店訂單號碼:</span>
            <p className="font-medium text-blue-900">{orderData.orderNo}</p>
          </div>
          
          {payuniResult?.TradeNo ? (
            <div>
              <span className="text-blue-600 font-medium">PayUni 交易號碼:</span>
              <p className="font-medium text-blue-900 font-mono text-xs">{String(payuniResult.TradeNo)}</p>
            </div>
          ) : null}
          
          <div>
            <span className="text-blue-600 font-medium">交易狀態:</span>
            <p className="font-medium text-blue-900">
              {payuniResult?.TradeStatus ? formatValue('TradeStatus', payuniResult.TradeStatus) : orderData.paymentStatus}
            </p>
          </div>
          
          <div>
            <span className="text-blue-600 font-medium">付款方式:</span>
            <p className="font-medium text-blue-900">
              {payuniResult?.PaymentType ? formatValue('PaymentType', payuniResult.PaymentType) : orderData.paymentMethod}
            </p>
          </div>
          
          <div>
            <span className="text-blue-600 font-medium">交易金額:</span>
            <p className="font-medium text-blue-900">
              {payuniResult?.TradeAmt ? formatValue('TradeAmt', payuniResult.TradeAmt) : `NT$ ${(orderData.eventPrice || 0).toLocaleString()}`}
            </p>
          </div>
          
          {(payuniResult?.PaymentDay || orderData.paymentCompletedAt) ? (
            <div>
              <span className="text-blue-600 font-medium">付款時間:</span>
              <p className="font-medium text-blue-900">
                {String(payuniResult?.PaymentDay) || new Date(orderData.paymentCompletedAt).toLocaleString('zh-TW')}
              </p>
            </div>
          ) : null}
        </div>
      </div>



      {/* ATM 轉帳資訊區塊 */}
      {(payuniResult && payuniResult.PaymentType === '2' && (payuniResult.OffPayNo || payuniResult.OffChannel)) ? (
        <div className="bg-green-50 border border-green-200 rounded-lg p-4 mb-6">
          <h3 className="font-medium text-green-900 mb-3 flex items-center">
            🏧 ATM 轉帳資訊
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
            {payuniResult.OffPayNo ? (
              <div>
                <span className="text-green-600 font-medium">繳費代碼:</span>
                <p className="font-medium text-green-900 font-mono">{String(payuniResult.OffPayNo)}</p>
              </div>
            ) : null}

            {payuniResult.OffChannel ? (
              <div>
                <span className="text-green-600 font-medium">取號通路:</span>
                <p className="font-medium text-green-900">{String(payuniResult.OffChannel)}</p>
              </div>
            ) : null}

            {payuniResult.OffPayChannel ? (
              <div>
                <span className="text-green-600 font-medium">繳費通路:</span>
                <p className="font-medium text-green-900">{String(payuniResult.OffPayChannel)}</p>
              </div>
            ) : null}

            {payuniResult.OffExpireTime ? (
              <div>
                <span className="text-green-600 font-medium">繳費期限:</span>
                <p className="font-medium text-green-900">{String(payuniResult.OffExpireTime)}</p>
              </div>
            ) : null}
          </div>
        </div>
      ) : null}

      {/* 如果是 Google Sheets 資料，顯示詳細的報名資訊 */}
      {orderData.dataSource === 'Google Sheets' ? (
        <div className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <span className="text-gray-600 text-sm">報名者</span>
              <p className="font-medium">{orderData.name}</p>
            </div>
            <div>
              <span className="text-gray-600 text-sm">Email</span>
              <p className="font-medium">{orderData.email}</p>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <span className="text-gray-600 text-sm">活動名稱</span>
              <p className="font-medium">{orderData.eventName}</p>
            </div>
            <div>
              <span className="text-gray-600 text-sm">提交時間</span>
              <p className="font-medium">{new Date(orderData.submittedAt).toLocaleString('zh-TW')}</p>
            </div>
          </div>

          {orderData.sessionTimes.length > 0 ? (
            <div>
              <span className="text-gray-600 text-sm">選擇場次</span>
              <div className="mt-1">
                {orderData.sessionTimes.map((time, index) => (
                  <span key={index} className="inline-block bg-gray-100 text-gray-800 px-2 py-1 rounded text-sm mr-2 mb-1">
                    {time}
                  </span>
                ))}
              </div>
            </div>
          ) : null}

          {orderData.participationType ? (
            <div>
              <span className="text-gray-600 text-sm">參與類型</span>
              <p className="font-medium">{orderData.participationType}</p>
            </div>
          ) : null}
        </div>
      ) : null}
    </div>
  );
}
